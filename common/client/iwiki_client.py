import requests
import hashlib
import time
import random
from urllib.parse import urlparse
from typing import Dict, List, Optional, Any
from common.error.detailed_value_error import raise_value_error, ErrorCode

class IWikiClient:
    """
    IWiki API，用于获取文档内容、创建文档和追加文档内容。
    调用文档：https://iwiki.woa.com/p/36307200
    swagger文档：https://iwiki.woa.com/p/4008410284
    """

    BASE_URL = "http://api-idc.sgw.woa.com/ebus/iwiki/prod/tencent/"
    DOC_BODY_PATH = "api/v2/doc/body"
    DOC_CREATE_PATH = "api/v2/doc/create"
    DOC_APPEND_PATH = "api/v2/doc/save/parts"
    VIKA_RECORDS_PATH = "api/vika/third/records"
    VIKA_FIELDS_PATH = "api/vika/third/fields"
    TABLE_BODY_PATH = "api/vika/third/records"

    def __init__(self, paas_id: str = 'yyb_ai', 
                    paas_token: str = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN', 
                    server: str = "http://api-idc.sgw.woa.com"):
        """
        初始化客户端

        :param paas_id: 太湖注册获得的应用id
        :param paas_token: 太湖注册获得的签名密钥
        :param server: 智能网关接入点域名，默认值为DevCloud区域
        """
        self.paas_id = paas_id
        self.paas_token = paas_token
        self.server = server.rstrip('/')
        self.session = requests.Session()

    def _generate_signature(self, timestamp: str, nonce: str) -> str:
        """
        生成请求签名

        :param timestamp: 时间戳字符串
        :param nonce: 随机字符串
        :return: 大写的sha256签名字符串
        """
        raw_string = timestamp + self.paas_token + nonce + timestamp
        signature = hashlib.sha256(raw_string.encode()).hexdigest().upper()
        return signature
    
    def _extract_doc_id_from_url(self, url: str) -> str | None:
        """
        从iwiki链接中提取文档ID

        示例：
        https://iwiki.woa.com/p/4008410284#%E8%8E%B7%E5%8F%96%E6%96%87%E6%A1%A3%E5%86%85%E5%AE%B9
        返回 '4008410284'
        https://iwiki.woa.com/p/4007810266
        返回 '4007810266'

        :param url: 文档链接
        :return: 文档ID字符串，提取失败返回None
        """
        try:
            parsed = urlparse(url)
            # 路径形如 /p/4008410284
            parts = parsed.path.strip('/').split('/')
            if len(parts) >= 2 and parts[0] == 'p':
                doc_id = parts[1]
                if doc_id.isdigit():
                    return doc_id
            return None
        except Exception:
            return None

    def get_doc_body(self, iwiki_url: str, bodymode: str = 'md') -> str | None:
        """
        获取文档正文内容

        :param iwiki_url: iwiki文档链接
        :param bodymode: 返回内容格式，默认 'md'，可选 'view' 等
        :return: 文档正文字符串，失败返回 None
        """
        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.DOC_BODY_PATH}"
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)

        headers = {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }

        doc_id = self._extract_doc_id_from_url(iwiki_url)
        if not doc_id:
            # raise ValueError("无效的iwiki链接")
            raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")

        params = {
            'id': doc_id,
            'bodymode': bodymode
        }

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get('msg') == 'ok':
                return result['data']['body']
            else:
                # raise ValueError(f"未能获取到iwiki文档内容: {result.get('msg')}")
                raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未能获取到iwiki文档内容: {result.get('msg')}")
                return None
        except requests.RequestException as e:
            # raise ValueError(f"未能获取到iwiki文档内容: {e}")
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未能获取到iwiki文档内容: {e}")
    
    def get_table_body(self, iwiki_url: str = None, doc_id: str = None, view_id: str = None) -> Optional[Dict]:
        """
        获取iwiki表格内容

        Args:
            iwiki_url: iwiki文档链接
            bodymode: 返回内容格式，默认 'md'(markdown)，可选 'view' (html)等
            view_id: 视图ID，可选

        Returns:
            Optional[Dict]: 表格内容字典，失败返回 None

        Raises:
            DetailedValueError: 
                - 当iwiki链接无效时
                - 当API调用失败时
                - 当未能获取到表格内容时
        """
        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.TABLE_BODY_PATH}"
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)


        headers = {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }
        if not iwiki_url and not doc_id:
            raise ValueError("必须提供iwiki_url或doc_id")
        
        if doc_id is None:
            doc_id = self._extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")
        print(f"doc_id: {doc_id}")
        params = {
            'doc_id': doc_id,
            'pageNum': 1,
            'pageSize': 100,
            'viewId': view_id
        }

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()
            return result
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未能获取到iwiki表格内容: {e}")
    
    def delete_table_record(self, recordIds: str, iwiki_url: str = None, doc_id: str = None):
        """删除iwiki表格记录"""
        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.TABLE_BODY_PATH}"
        # timestamp = str(int(time.time()))
        # nonce = str(random.randint(1000, 9999))
        # signature = self._generate_signature(timestamp, nonce)

        # headers = {
        #     'Content-Type': 'application/json',
        #     'x-rio-paasid': self.paas_id,
        #     'x-rio-timestamp': timestamp,
        #     'x-rio-nonce': nonce,
        #     'x-rio-signature': signature
        # }
        # if not iwiki_url and not doc_id:
        #     raise ValueError("必须提供iwiki_url或doc_id")
        # print(f"iwiki_url: {iwiki_url}")
        # if doc_id is None:
        #     doc_id = self._extract_doc_id_from_url(iwiki_url)
        #     if not doc_id:
        #         raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")
        # print(f"doc_id: {doc_id}")
        # params = {
        #     'doc_id': doc_id,
        #     'recordIds': recordIds
        # }

        # try:
        #     response = self.session.delete(url, params=params, headers=headers, timeout=10)
        #     response.raise_for_status()
        #     result = response.json()
        #     print("删除：", result)
        #     # if result.get('msg') == 'ok':
        #     #     return result['data']
        #     # else:
        #     #     error_msg = result.get('msg', '未知错误')
        #     #     error_code = result.get('code', '未知错误码')
        #     #     raise_value_error(
        #     #         ErrorCode.IWIKI_API_ERROR, 
        #     #         message=f"未能获取到iwiki表格内容: {error_msg} (错误码: {error_code})"
        #     #     )
        #     #     return None
        # except requests.RequestException as e:
        #     raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未能获取到iwiki表格内容: {e}")
        

        paasId = 'yyb_ai'  # 在太湖注册获得的应用id
        paasToken = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN'  # 在太湖注册获得的签名密钥
        server = "http://api-idc.sgw.woa.com"  # 智能网关在OA区的接入点域名
        path = "/ebus/iwiki/prod/tencent/api/vika/third/fields"  # 在太湖订阅接口成功后，获得的接口path
        params = {"doc_id":int(doc_id),"recordIds":recordIds}  # 接口入参
        print(f"recordIds: {recordIds}")
        print(f"params: {params}")
        timestamp = str(int(time.time()))  # 生成时间戳，注意服务器的时间与标准时间差不能大于180秒
        nonce = str(random.randint(1000, 9999))  # 随机字符串，十分钟内不重复即可
        signature = hashlib.sha256()
        string = timestamp + paasToken + nonce + timestamp
        signature.update(string.encode())
        signature = signature.hexdigest().upper()  # 输出大写的结果
        header = {}
        #  设置鉴权参数，如果此参数设置错误，将触发“AGW.xxxxx”类型的错误，详见3.4章节
        header['Content-Type'] = 'application/json'
        header['x-rio-paasid'] = paasId
        header['x-rio-nonce'] = nonce
        header['x-rio-timestamp'] = timestamp
        header['x-rio-signature'] = signature
        req1 = requests.Session()
        # 这里主要展示http head的构造，省略了http body的构造。
        response = req1.delete(url=url, params=params,headers=header)
        code = response.status_code
        print(server + path)
        print(code)
        print(response.headers)
        print(response.text)



    def create_document(self, body: str, title: str, parentid: int, spacekey: str = "ailogs",
                       contenttype: str = "MD", body_mode: str = "", vika_mode: str = "") -> dict:
        """
        创建新的iwiki文档

        :param body: 文档内容
        :param title: 文档标题
        :param spacekey: 空间key
        :param parentid: 父文档ID
        :param contenttype: 内容类型，默认"MD"，设置为"VIKA"可创建多维表格
        :param body_mode: 内容模式，默认空字符串
        :param vika_mode: 多维表格模式，当contenttype="VIKA"时可设置为"excel"
        :return: 包含解析结果的字典，格式：
                {
                    'success': bool,
                    'docid': int | None,
                    'request_id': str | None,
                    'message': str,
                    'raw_response': dict
                }
        """
        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.DOC_CREATE_PATH}"
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)

        headers = {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }

        params = {
            "body": body,
            "body_mode": body_mode,
            "contenttype": contenttype,
            "spacekey": spacekey,
            "title": title,
            "parentid": parentid
        }

        # 如果是创建多维表格，添加vika_mode参数
        if contenttype == "VIKA" and vika_mode:
            params["vika_mode"] = vika_mode

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_create_document_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"创建iwiki文档失败: {e}")

    def _parse_create_document_response(self, response: dict) -> dict:
        """
        解析创建文档的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'docid': None,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = '文档创建成功'
            parsed_result['request_id'] = response.get('request_id')

            # 提取docid
            data = response.get('data', {})
            if isinstance(data, dict):
                parsed_result['docid'] = data.get('docid') or data.get('id')
        else:
            parsed_result['message'] = f"文档创建失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def append_document(self, iwiki_url: str, title: str, content_to_append: str) -> dict:
        """
        向现有iwiki文档追加内容

        :param document_id: 文档ID
        :param title: 文档标题
        :param content_to_append: 要追加的内容
        :return: 包含解析结果的字典，格式：
                {
                    'success': bool,
                    'request_id': str | None,
                    'message': str,
                    'raw_response': dict
                }
        """
        document_id = self._extract_doc_id_from_url(iwiki_url)

        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.DOC_APPEND_PATH}"
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)

        headers = {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }

        params = {
            "id": int(document_id),
            "title": title,
            "after": content_to_append
        }

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_append_document_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"追加iwiki文档内容失败: {e}")

    def _parse_append_document_response(self, response: dict) -> dict:
        """
        解析追加文档内容的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = '文档内容追加成功'
            parsed_result['request_id'] = response.get('request_id')
        else:
            parsed_result['message'] = f"文档内容追加失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def post_vika_records(self, iwiki_url: str = None, doc_id: int = None, field_key: str = "name",
                         fields_list: list[dict] = None, records: list[dict] = None) -> dict:
        """
        向vika表格添加记录

        :param doc_id: 文档ID
        :param field_key: 字段键名，默认为"name"
        :param fields_list: 字段列表，每个元素为字段字典，会自动封装为records格式
        :param records: 完整的记录列表（如果提供此参数，fields_list将被忽略）
        :return: 包含解析结果的字典，格式：
                {
                    'success': bool,
                    'request_id': str | None,
                    'message': str,
                    'raw_response': dict
                }

        使用示例1（推荐，使用fields_list）：
        fields_list = [
            {
                "需求类型": "产品",
                "需求版本": "121"
            }
        ]
        result = client.post_vika_records(4015287369, fields_list=fields_list)

        使用示例2（完整格式，使用records）：
        records = [{
            "fields": {
                "需求类型": "产品",
                "需求版本": "121"
            }
        }]
        result = client.post_vika_records(4015287369, records=records)
        """
        # 参数验证
        if doc_id is None and iwiki_url is None:
            raise ValueError("doc_id和iwiki_url不能同时为空")
        
        if fields_list is None and records is None:
            raise ValueError("必须提供 fields_list 或 records 参数之一")
        
        # doc_id为空，使用iwiki_url提取doc_id
        if doc_id is None:
            doc_id = self._extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")

        # 如果提供了fields_list，自动封装为records格式
        if records is None:
            records = [{"fields": fields} for fields in fields_list]
        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.VIKA_RECORDS_PATH}"
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)

        headers = {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }

        params = {
            "doc_id": int(doc_id),
            "fieldKey": field_key,
            "records": records
        }

        print(f"请求参数：{params}")

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_vika_records_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"添加vika记录失败: {e}")

    def _parse_vika_records_response(self, response: dict) -> dict:
        """
        解析vika记录添加的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = 'vika记录添加成功'
            parsed_result['request_id'] = response.get('request_id')
        else:
            parsed_result['message'] = f"vika记录添加失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def get_vika_fields(self, iwiki_url: str = None, doc_id: int = None, view_id: str = None) -> dict:
        """
        获取多维表格的字段信息

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param view_id: 视图ID（可选）
        :return: 包含解析结果的字典，格式：
                {
                    'success': bool,
                    'fields': list[dict] | None,  # 字段列表
                    'request_id': str | None,
                    'message': str,
                    'raw_response': dict
                }
        """
        # 参数验证
        if doc_id is None and iwiki_url is None:
            raise ValueError("doc_id和iwiki_url不能同时为空")

        # doc_id为空，使用iwiki_url提取doc_id
        if doc_id is None:
            doc_id = self._extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")

        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.VIKA_FIELDS_PATH}"
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)

        headers = {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }

        params = {
            "doc_id": int(doc_id)
        }

        if view_id:
            params["viewId"] = view_id

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_vika_fields_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"获取vika字段失败: {e}")

    def _parse_vika_fields_response(self, response: dict) -> dict:
        """
        解析获取vika字段的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'fields': None,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = '获取vika字段成功'
            parsed_result['request_id'] = response.get('request_id')
            parsed_result['fields'] = response.get('data', {}).get('fields', [])
        else:
            parsed_result['message'] = f"获取vika字段失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def delete_vika_field(self, iwiki_url: str = None, doc_id: int = None, field_id: str = None) -> dict:
        """
        删除多维表格的字段

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param field_id: 字段ID
        :return: 包含解析结果的字典，格式：
                {
                    'success': bool,
                    'request_id': str | None,
                    'message': str,
                    'raw_response': dict
                }
        """
        # 参数验证
        if doc_id is None and iwiki_url is None:
            raise ValueError("doc_id和iwiki_url不能同时为空")

        if field_id is None:
            raise ValueError("field_id不能为空")

        # doc_id为空，使用iwiki_url提取doc_id
        if doc_id is None:
            doc_id = self._extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")

        # url = f"{self.server}/ebus/iwiki/prod/tencent/{self.VIKA_FIELDS_PATH}"
        # timestamp = str(int(time.time()))
        # nonce = str(random.randint(1000, 9999))
        # signature = self._generate_signature(timestamp, nonce)

        # headers = {
        #     'x-rio-paasid': self.paas_id,
        #     'x-rio-timestamp': timestamp,
        #     'x-rio-nonce': nonce,
        #     'x-rio-signature': signature
        # }

        # params = {
        #     'doc_id': int(doc_id),
        #     'fieldId': field_id
        # }

        # print("url = ",url)
        # print("params = ",params)
        # print("headers = ",headers)

        # try:
        #     # 尝试使用 URL 参数方式删除字段
        #     response = self.session.delete(url, headers=headers, params=params, timeout=10)
        #     response.raise_for_status()
        #     result = response.json()
        #     print("result = ",result)

        #     # 解析响应结果
        #     return self._parse_vika_field_operation_response(result, "删除")
        # except requests.RequestException as e:
        #     raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"删除vika字段失败: {e}")
        


        try:
            paasId = 'yyb_ai'  # 在太湖注册获得的应用id
            paasToken = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN'  # 在太湖注册获得的签名密钥
            server = "http://api-idc.sgw.woa.com"  # 智能网关在OA区的接入点域名
            path = "/ebus/iwiki/prod/tencent/api/vika/third/fields"  # 在太湖订阅接口成功后，获得的接口path
            params = {"doc_id":int(doc_id),"fieldId":field_id}  # 接口入参
            timestamp = str(int(time.time()))  # 生成时间戳，注意服务器的时间与标准时间差不能大于180秒
            nonce = str(random.randint(1000, 9999))  # 随机字符串，十分钟内不重复即可
            signature = hashlib.sha256()
            string = timestamp + paasToken + nonce + timestamp
            signature.update(string.encode())
            signature = signature.hexdigest().upper()  # 输出大写的结果
            header = {}
            #  设置鉴权参数，如果此参数设置错误，将触发“AGW.xxxxx”类型的错误，详见3.4章节
            header['x-rio-paasid'] = paasId
            header['x-rio-nonce'] = nonce
            header['x-rio-timestamp'] = timestamp
            header['x-rio-signature'] = signature
            req1 = requests.Session()
            # 这里主要展示http head的构造，省略了http body的构造。
            response = req1.delete(url=server+path, params=params,headers=header)
            code = response.status_code
            print(server + path)
            print(code)
            print(response.headers)
            print(response.text)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"删除vika字段失败: {e}")

    def add_vika_field(self, iwiki_url: str = None, doc_id: int = None, field_type: str = None,
                      field_name: str = None, field_property: dict = None) -> dict:
        """
        添加多维表格的字段

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param field_type: 字段类型，如 "SingleText", "Text", "MultiSelect"
        :param field_name: 字段名称
        :param field_property: 字段属性配置
        :return: 包含解析结果的字典，格式：
                {
                    'success': bool,
                    'field_id': str | None,  # 新创建的字段ID
                    'request_id': str | None,
                    'message': str,
                    'raw_response': dict
                }

        使用示例：
        # 添加单行文本字段
        result = client.add_vika_field(
            doc_id=4015298928,
            field_type="SingleText",
            field_name="标题",
            field_property={"defaultValue": "默认文本文本"}
        )

        # 添加多行文本字段
        result = client.add_vika_field(
            doc_id=4015298928,
            field_type="Text",
            field_name="描述"
        )

        # 添加多选字段
        result = client.add_vika_field(
            doc_id=4015298928,
            field_type="MultiSelect",
            field_name="标签",
            field_property={
                "options": [{"name": "选项1", "color": "deepPurple_0"}],
                "defaultValue": "选项1"
            }
        )
        """
        # 参数验证
        if doc_id is None and iwiki_url is None:
            raise ValueError("doc_id和iwiki_url不能同时为空")

        if field_type is None:
            raise ValueError("field_type不能为空")

        if field_name is None:
            raise ValueError("field_name不能为空")

        # doc_id为空，使用iwiki_url提取doc_id
        if doc_id is None:
            doc_id = self._extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")

        url = f"{self.server}/ebus/iwiki/prod/tencent/{self.VIKA_FIELDS_PATH}"
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)

        headers = {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }

        params = {
            "doc_id": int(doc_id),
            "type": field_type,
            "name": field_name
        }

        if field_property:
            params["property"] = field_property

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_add_vika_field_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"添加vika字段失败: {e}")

    def _parse_vika_field_operation_response(self, response: dict, operation: str) -> dict:
        """
        解析vika字段操作的API响应（删除等操作）

        :param response: API原始响应
        :param operation: 操作名称，如"删除"
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = f'vika字段{operation}成功'
            parsed_result['request_id'] = response.get('request_id')
        else:
            parsed_result['message'] = f"vika字段{operation}失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def _parse_add_vika_field_response(self, response: dict) -> dict:
        """
        解析添加vika字段的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'field_id': None,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = 'vika字段添加成功'
            parsed_result['request_id'] = response.get('request_id')

            # 提取新创建的字段ID
            data = response.get('data', {})
            if isinstance(data, dict):
                parsed_result['field_id'] = data.get('id')
        else:
            parsed_result['message'] = f"vika字段添加失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def create_vika_table_and_customize_fields(self, title: str, parentid: int,
                                             custom_fields: list[dict] = None) -> dict:
        """
        创建多维表格并自定义字段（删除默认字段，添加自定义字段）

        :param title: 表格标题
        :param parentid: 父文档ID
        :param custom_fields: 自定义字段列表，每个元素包含 type, name, property
        :return: 包含操作结果的字典，格式：
                {
                    'success': bool,
                    'doc_id': int | None,
                    'created_fields': list[dict] | None,  # 成功创建的字段信息
                    'deleted_fields': list[str] | None,   # 成功删除的字段ID
                    'message': str,
                    'details': dict  # 详细的操作结果
                }

        使用示例：
        custom_fields = [
            {
                "type": "SingleText",
                "name": "需求标题",
                "property": {"defaultValue": "请输入标题"}
            },
            {
                "type": "Text",
                "name": "需求描述"
            },
            {
                "type": "MultiSelect",
                "name": "需求类型",
                "property": {
                    "options": [
                        {"name": "产品需求", "color": "deepPurple_0"},
                        {"name": "技术需求", "color": "blue_0"}
                    ]
                }
            }
        ]

        result = client.create_vika_table_and_customize_fields(
            title="需求管理表",
            parentid=189268868,
            custom_fields=custom_fields
        )
        """
        result = {
            'success': False,
            'doc_id': None,
            'created_fields': [],
            'deleted_fields': [],
            'message': '',
            'details': {}
        }
        # 1. 创建多维表格
        create_result = self.create_document(
            body="",
            title=title,
            parentid=parentid,
            contenttype="VIKA",
            vika_mode="excel"
        )


        doc_id = create_result['docid']
        result['doc_id'] = doc_id

        try:
            # 删除默认空行数据
            # 获取表格记录
            record_result = self.get_table_body(doc_id=doc_id)
            if not record_result:
                print("获取记录失败 ", record_result)
            print(f"获取到的记录: {record_result}")
            record_ids = [record['recordId'] for record in record_result.get('data', {}).get('records', [])]
            print(f"记录ID列表: {record_ids}")
            # self.delete_table_record(doc_id=doc_id, recordIds=record_ids)

            # 2. 获取默认字段
            fields_result = self.get_vika_fields(doc_id=doc_id)

            fields = fields_result['fields']
            print(f"字段信息: {fields}")
            if len(fields) < 2:
                result['message'] = f"默认字段数量不足，期望至少2个，实际{len(fields)}个"
                return result

            # 3. 删除后两个默认字段（第一个字段不能删除）
            fields_to_delete = fields[1:]  # 获取除了第1个字段的其余字段
            print(f"待删除字段: {fields_to_delete}")
            delete_results = []

            for field in fields_to_delete:
                print(f"删除字段: {field.get('name')}")
                field_id = field.get('id')
                print(f"删除字段ID: {field_id}")
                if field_id:
                    delete_result = self.delete_vika_field(doc_id=doc_id, field_id=field_id)
                    print(f"删除字段结果: {delete_result}")
                    delete_results.append(delete_result) 

            print("=== 删除默认字段完成 ===")


            # 4. 添加自定义字段
            if custom_fields:
                for field_config in custom_fields:
                    self.add_vika_field(
                        doc_id=doc_id,
                        field_type=field_config.get('type'),
                        field_name=field_config.get('name'),
                        field_property=field_config.get('property')
                    )
                

        except Exception as e:
            result['message'] = f"操作过程中发生异常: {str(e)}"
            result['details']['exception'] = str(e)

        return result


if __name__ == "__main__":
    # 使用示例
    paas_id = 'yyb_ai'
    paas_token = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN'
    client = IWikiClient(paas_id, paas_token)

    # # 示例1：获取文档内容
    # iwiki_url = 'https://iwiki.woa.com/p/4014594307'
    # body_content = client.get_doc_body(iwiki_url)
    # if body_content:
    #     print("获取到的body内容：")
    #     print(body_content)
    # else:
    #     print("未能获取到body内容")

    # # 示例2：创建新文档
    # sample_body = """
    # ## 启动速度
    
    # | 灰度新用户 | QUA |常规热启动<br>(gap<50ms) | 常规冷启动<br>(gap<100ms) |
    # |--------|--------------|---------|------|
    # |实验组1|TMAF_899_P_7921|11|22|
    # |对照组|TMAF_899_P_7921|33|44|
    # """
    
    # try:
    #     result = client.create_document(
    #         body=sample_body,
    #         title="测试文档标题",
    #         spacekey="ailogs",
    #         parentid=4015157988
    #     )
    #     print("创建文档结果：", result)
    #     if result['success']:
    #         print(f"文档创建成功，docid: {result['docid']}")
    #         print(f"请求ID: {result['request_id']}")
    #     else:
    #         print(f"文档创建失败: {result['message']}")
    # except Exception as e:
    #     print("创建文档失败：", e)

    # # 示例3：追加文档内容
    # append_content = """

    # ## 新增内容
    # 这是追加的内容
    # """

    # try:
    #     result = client.append_document(
    #         iwiki_url='https://iwiki.woa.com/p/4015180169',
    #         title="889版本灰度数据",
    #         content_to_append=append_content
    #     )
    #     print("追加内容结果：", result)
    #     if result['success']:
    #         print(f"内容追加成功，请求ID: {result['request_id']}")
    #     else:
    #         print(f"内容追加失败: {result['message']}")
    # except Exception as e:
    #     print("追加内容失败：", e)

    # 示例4：创建多维表格并自定义字段（完整流程）
    # custom_fields = [
    #     {
    #         "type": "SingleText",
    #         "name": "需求标题",
    #         "property": {"defaultValue": "请输入标题"}
    #     },
    #     {
    #         "type": "Text",
    #         "name": "需求描述"
    #     },
    #     {
    #         "type": "MultiSelect",
    #         "name": "需求类型",
    #         "property": {
    #             "options": [
    #                 {"name": "产品需求", "color": "deepPurple_0"},
    #                 {"name": "技术需求", "color": "blue_0"},
    #                 {"name": "BUG", "color": "red_0"}
    #             ]
    #         }
    #     }
    # ]

    # try:
    #     result = client.create_vika_table_and_customize_fields(
    #         title="需求管理表",
    #         parentid=4015287337,
    #         custom_fields=custom_fields
    #     )
    #     print("创建并自定义表格结果：", result)
    #     if result['success']:
    #         print(f"表格创建成功，docid: {result['doc_id']}")
    #         print(f"创建的字段: {result['created_fields']}")
    #     else:
    #         print(f"操作失败: {result['message']}")
    #         print(f"详细信息: {result['details']}")
    # except Exception as e:
    #     print("创建并自定义表格失败：", e)


    #  =========  删除测试  ==============

    # fields_result = client.get_vika_fields(doc_id=4015299613)
    # print("fields_result = ", fields_result)

    # fields = fields_result['fields']
        

    # # 3. 删除后两个默认字段（第一个字段不能删除）
    # fields_to_delete = fields  # 获取第2和第3个字段
    # print("fields_to_delete = ", fields_to_delete)

    # for field in fields_to_delete:
    #     field_id = field.get('id')
    #     print(f"字段名： {field.get('name')} (ID: {field_id})")

    # delete_result = client.delete_vika_field(doc_id=4015299613, field_id="fldaY3sjmK2G8")

    # 示例5：分步操作示例
    # # 5.1 创建多维表格
    # result = client.create_document(
    #     body="",
    #     title="测试文档标题1",
    #     parentid=189268868,
    #     contenttype="VIKA",
    #     vika_mode="excel"
    # )
    # print("创建文档结果：", result)
    # if result['success']:
    #     doc_id = result['docid']
    #     print(f"文档创建成功，docid: {doc_id}")
    #
    #     # 5.2 获取字段信息
    #     fields_result = client.get_vika_fields(doc_id=doc_id)
    #     print("获取字段结果：", fields_result)
    #     if fields_result['success']:
    #         fields = fields_result['fields']
    #         print(f"获取到 {len(fields)} 个字段")
    #         for i, field in enumerate(fields):
    #             print(f"字段{i+1}: {field.get('name')} (ID: {field.get('id')})")
    #
    #         # 5.3 删除后两个字段（第一个字段不能删除）
    #         if len(fields) >= 3:
    #             for field in fields[1:3]:  # 删除第2和第3个字段
    #                 field_id = field.get('id')
    #                 if field_id:
    #                     delete_result = client.delete_vika_field(doc_id=doc_id, field_id=field_id)
    #                     print(f"删除字段 {field.get('name')} 结果：", delete_result)
    #
    #         # 5.4 添加自定义字段
    #         # 添加单行文本字段
    #         add_result1 = client.add_vika_field(
    #             doc_id=doc_id,
    #             field_type="SingleText",
    #             field_name="需求标题",
    #             field_property={"defaultValue": "请输入标题"}
    #         )
    #         print("添加单行文本字段结果：", add_result1)
    #
    #         # 添加多行文本字段
    #         add_result2 = client.add_vika_field(
    #             doc_id=doc_id,
    #             field_type="Text",
    #             field_name="需求描述"
    #         )
    #         print("添加多行文本字段结果：", add_result2)
    #
    #         # 添加多选字段
    #         add_result3 = client.add_vika_field(
    #             doc_id=doc_id,
    #             field_type="MultiSelect",
    #             field_name="需求类型",
    #             field_property={
    #                 "options": [
    #                     {"name": "产品需求", "color": "deepPurple_0"},
    #                     {"name": "技术需求", "color": "blue_0"}
    #                 ]
    #             }
    #         )
    #         print("添加多选字段结果：", add_result3)

    # 示例6：添加vika记录（推荐方式，使用fields_list）
    fields_list = [{
	'需求类型': '产品需求',
	'需求': '云盘后台中转层：动态扩容能力支持',
	'需求单': 'https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124925650',
	'MR链接': 'https://git.woa.com/yyb-android/TencentMobileAssistant/-/merge_requests/11208',
	'负责产品': 'yunuoma',
	'终端开发': 'shasionwu',
	'是否预埋': '是',
	'测试关注重点': '不涉及',
	'使用的开关系统，值、开关状态': '开关名称： key_enable_cloud_disk_capacity_request，取值：默认 true， 开关打开方式：Shiply',
	'特性实验链接或特性实验报告': '主分支开发，无实验报告',
	'标题': '需求版本900'
}, {
	'需求类型': '产品需求',
	'需求': 'feat: 详情页AI问答卡复制按钮及图片展示优化',
	'需求单': 'https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124757929',
	'MR链接': 'https://git.woa.com/yyb-android/TencentMobileAssistant/-/merge_requests/11198',
	'负责产品': 'elainesyliu',
	'终端开发': 'qinyixu',
	'是否预埋': '否',
	'测试关注重点': '不涉及',
	'使用的开关系统，值、开关状态': '开关名称： xxxx，取值：X/Y/Z， 开关打开方式：XXX',
	'特性实验链接或特性实验报告': '主分支开发，无实验报告'
}]
    print(fields_list)
    
    iwiki_url='https://iwiki.woa.com/p/4015333167'
    

    
    try:
        result = client.post_vika_records(
            iwiki_url=iwiki_url,
            fields_list=fields_list
        )
        print("添加vika记录结果：", result)
        if result['success']:
            print(f"vika记录添加成功，请求ID: {result['request_id']}")
        else:
            print(f"vika记录添加失败: {result['message']}")
    except Exception as e:
        print("添加vika记录失败：", e)
