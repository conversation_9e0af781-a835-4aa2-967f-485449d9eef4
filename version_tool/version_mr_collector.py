from typing import Dict, List, Optional, Any

from common.client.git_client import GitClient
from common.client.iwiki_client import IWikiClient

class VersionMrCollector:
    def __init__(self):
        self.git_client = GitClient.create_default_client()
        self.iwiki_client = IWikiClient()
    
    def collect_mr_info(self, branch_name: str):
        """ 收集分支差异mr的详细信息 """
        detailed_mrs = self.git_client.get_branch_diff_mrs_with_details(from_branch=branch_name, to_branch="master", days_back=30)
        if not detailed_mrs:
            print("未找到差异 MR")
            raise ValueError("未找到差异 MR")
        
        return detailed_mrs
    
    def create_table(self, version: str):
        """ 创建多维表格 设置表头 """
        mr_info = [
            {
                "type": "Text",
                "name": "需求类型"
            },
            {
                "type": "Text",
                "name": "需求"
            },
            {
                "type": "Text",
                "name": "需求单"
            },
            {
                "type": "Text",
                "name": "MR链接"
            },
            {
                "type": "Text",
                "name": "负责产品"
            },
            {
                "type": "Text",
                "name": "终端开发"
            },
            {
                "type": "Text",
                "name": "是否预埋"
            },
            {
                "type": "Text",
                "name": "测试关注重点"
            },
            {
                "type": "Text",
                "name": "使用的开关系统，值、开关状态"
            },
            {
                "type": "Text",
                "name": "特性实验链接或特性实验报告"
            },
            {
                "type": "Text",
                "name": "灰度班车"
            },
            {
                "type": "Text",
                "name": "分组"
            }
        ]
        result = self.iwiki_client.create_vika_table_and_customize_fields(
            title=f"{version}版本需求列表",
            parentid=4015287337,
            custom_fields=mr_info
        )
        print("创建并自定义表格结果：", result)
        return result['doc_id']
        # if result['success']:
        #     print(f"表格创建成功，docid: {result['doc_id']}")
        #     print(f"创建的字段: {result['created_fields']}")
        #     return result['doc_id']
        # else:
        #     print(f"操作失败: {result['message']}")
        #     print(f"详细信息: {result['details']}")
        #     raise ValueError("创建表格失败")
    
    def fill_table(self, doc_id: str, version: str, mr_info: List[Dict[str, Any]]):
        """ 填充表格 """
        # 构建字段列表
        # 产品需求
        product_list = []
        # 技术需求
        tech_list = []
        # bug
        bug_list = []
        # 最后填充的字段列表
        fields_list = []
        print("填充的字段列表 0 ：", fields_list)
        try:
            print(f"第一个 mr 信息：{mr_info[0]}")
            for mr in mr_info:
                type = mr["type"]
                fill_info = {
                    # "需求类型": type, # 只在第一个元素添加
                    "需求": mr.get("title", ""),
                    "需求单": mr.get("tapd_url", ""),
                    "MR链接": mr.get("mr_url", ""),
                    "负责产品": mr.get("creator", ""),
                    "终端开发": mr.get("author", ""),
                    "是否预埋": mr.get("is_pre_embedded", ""),
                    "测试关注重点": mr.get("integration_test_focus", ""),
                    "使用的开关系统，值、开关状态": mr.get("switch_description", ""),
                    "特性实验链接或特性实验报告": mr.get("experiment_report_url", "")
                    # "灰度班车": "" # 分组手动填写
                    # "分组": "" # 分组手动填写
                }
                # fill_info = {
                #     "需求类型": type,
                #     "需求": mr["title"],
                #     "需求单": mr["tapd_url"],
                #     "MR链接": mr["mr_url"],
                #     "负责产品": mr["creator"],
                #     "终端开发": mr["author"],
                #     "是否预埋": mr["is_pre_embedded"],
                #     "测试关注重点": mr["integration_test_focus"],
                #     "使用的开关系统，值、开关状态": mr["switch_description"],
                #     "特性实验链接或特性实验报告": mr["experiment_report_url"]
                # }
                if type == "产品需求":
                    print("产品需求:", fill_info.get("需求"))
                    product_list.append(fill_info)
                elif type == "技术需求":
                    print("技术需求:", fill_info.get("需求"))
                    tech_list.append(fill_info)
                elif type == "BUG":
                    print("BUG:", fill_info.get("需求"))
                    bug_list.append(fill_info)

            # 在每个类型的第一个元素添加需求类型
            if product_list:
                print("产品需求列表:", product_list)
                product_list[0]["需求类型"] = "产品需求"
                print("1")
                product_list[0]["标题"] = f"需求版本{version}"
                print("2")
                # 在每个列表的最后添加两个空元素
                # product_list.append({})
                # print("3")
                # product_list.append({})
                # print("4")

            if tech_list:
                print("技术需求列表:", tech_list)
                tech_list[0]["需求类型"] = "技术需求"
                print("5")
                # 在每个列表的最后添加两个空元素
                # tech_list.append({})
                # print("6")
                # tech_list.append({})
                # print("7")

            if bug_list:
                print("8")
                bug_list[0]["需求类型"] = "BUG"

            fields_list.extend(product_list)
            print("填充的字段列表1：", fields_list)
            fields_list.extend(tech_list)
            print("9")
            fields_list.extend(bug_list)
            print("填充的字段列表：", fields_list)
            
            self.batch_post_records(doc_id=doc_id, fields_list=fields_list)

            
        except Exception as e:
            print(f"填充表格失败：{e}")
    
    def batch_post_records(self, doc_id, fields_list, batch_size=10):
        """ 批量更新记录 多维表格更新记录最多一次性十条 """
        total = len(fields_list)
        for i in range(0, total, batch_size):
            batch = fields_list[i:i+batch_size]
            print(f"records: {batch}")
            result = self.iwiki_client.post_vika_records(doc_id=doc_id, fields_list=batch)
            print(f"已上传第{i}到{i+len(batch)-1}条记录，返回结果：", result)
        
        print("填充表格结果：", result)
        if result['success']:
            print("填充成功，请求ID: {result['request_id']}")
        else:
            print(f"填充失败: {result['message']}")
            raise ValueError("填充表格失败")
    
    def fill_table_with_mr_info(self, version: str):
        """ 流程函数：将指定版本需求信息填入表格 """
        doc_id = self.create_table(version=version)
        print(f"doc_id: {doc_id}")
        if not doc_id:
            raise ValueError("创建表格失败")

        branch_name = self._format_release(int(version)-1)
        print(f"branch_name: {branch_name}")
        mr_info = self.collect_mr_info(branch_name=branch_name)
        self.fill_table(doc_id=doc_id, version=version, mr_info=mr_info)
    
    def _format_release(self, version):
        # 把输入转成字符串
        s = str(version)
        # 用点分隔每个字符
        parts = list(s)
        # 拼接成 release- + 点分隔字符串
        return "release-" + ".".join(parts)


if __name__ == "__main__":
    collector = VersionMrCollector()
    collector.fill_table_with_mr_info(version="900")
    
