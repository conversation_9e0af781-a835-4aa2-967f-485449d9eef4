"""
工蜂 (Tencent Git) API 操作客户端
提供仓库对比、合并请求操作等 Git 相关功能
参考文档：https://git.woa.com/help/menu/api/repositorys.html
"""

import requests
import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from common.client.tapd_client import TapdClient


class GitClientError(Exception):
    """Git 客户端操作异常"""
    pass


class GitClient:
    """
    工蜂 (Tencent Git) API 交互客户端

    支持的操作包括：
    - 仓库对比（分支、提交、标签）
    - 合并请求操作
    - 文件变更跟踪
    - 获取mr对应的tapd单子
    """

    def __init__(self, project_id: str, private_token: str, base_url: str = "https://git.woa.com",
                 project_path: str = "yyb-android/TencentMobileAssistant"):
        """
        初始化 Git 客户端

        Args:
            project_id: Git 项目 ID
            private_token: 认证私钥
            base_url: Git API 基础 URL（默认：https://git.woa.com）
            project_path: 项目的全路径（默认：yyb-android/TencentMobileAssistant）
        """
        self.project_id = project_id
        self.private_token = private_token
        self.base_url = base_url.rstrip('/')
        self.project_path = project_path
        self.api_base = f"{self.base_url}/api/v3/projects/{self.project_id}"
        self.tapd_client = TapdClient()

    def _make_request(self, endpoint: str, params: Optional[Dict] = None, method: str = "GET") -> Dict[str, Any]:
        """
        向 Git API 发送 HTTP 请求

        Args:
            endpoint: API 端点（相对于项目基础路径）
            params: 查询参数
            method: HTTP 方法

        Returns:
            JSON 响应数据

        Raises:
            GitClientError: 请求失败时抛出
        """
        url = f"{self.api_base}/{endpoint.lstrip('/')}"

        if params is None:
            params = {}

        # 添加认证令牌
        params['private_token'] = self.private_token

        try:
            response = requests.request(method, url, params=params)

            if response.status_code != 200:
                raise GitClientError(
                    f"API 请求失败，状态码 {response.status_code}: {response.text}"
                )

            return response.json()

        except requests.RequestException as e:
            raise GitClientError(f"请求失败: {str(e)}")

    def compare_branches(self, from_ref: str, to_ref: str) -> Dict[str, Any]:
        """
        获取两个分支、提交或标签之间的差异内容
        参考文档：https://git.woa.com/help/menu/api/repositorys.html#%E8%8E%B7%E5%8F%96%E5%B7%AE%E5%BC%82%E5%86%85%E5%AE%B9

        API: GET /api/v3/projects/:id/repository/compare

        Args:
            from_ref: 源分支/提交/标签
            to_ref: 目标分支/提交/标签

        Returns:
            包含对比数据的字典，结构如下：
            {
                "commit": {...},
                "base_commit": {...},
                "commits": [...],
                "diffs": [...],
                "compare_timeout": bool,
                "compare_same_ref": bool,
                "over_flow": bool,
                "files_total": int,
                "commits_total": int,
                "ahead_by": int,
                "behind_by": int
            }
        """
        params = {
            'from': from_ref,
            'to': to_ref
        }

        return self._make_request('repository/compare', params)

    def get_merge_request(self, merge_request_id: int) -> Dict[str, Any]:
        """
        获取指定合并请求的详细信息

        API: GET /api/v3/projects/:id/merge_request/:merge_request_id

        Args:
            merge_request_id: 合并请求的 ID

        Returns:
            包含合并请求详细信息的字典
        """
        return self._make_request(f'merge_request/{merge_request_id}')

    def get_merge_requests(self,
                          state: str = 'merged',
                          target_branch: str = 'master',
                          order_by: str = 'resolve_at',
                          per_page: int = 200,
                          created_after: Optional[str] = None,
                          iids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        获取符合条件的合并请求列表

        API: GET /api/v3/projects/:id/merge_requests

        Args:
            state: 合并请求状态（默认：'merged'）
            target_branch: 目标分支过滤（默认：'master'）
            order_by: 排序字段（默认：'resolve_at'）
            per_page: 每页结果数量（默认：200）
            created_after: 过滤此日期后创建的 MR（ISO 格式）
            iids: 要过滤的 MR IID 列表

        Returns:
            合并请求字典列表
        """
        params = {
            'state': state,
            'target_branch': target_branch,
            'order_by': order_by,
            'per_page': per_page
        }

        if created_after:
            params['created_after'] = created_after

        if iids:
            params['iids[]'] = iids

        return self._make_request('merge_requests', params)

    def get_merge_request_changes(self, merge_request_id: int) -> Dict[str, Any]:
        """
        获取合并请求中的文件变更信息

        API: GET /api/v3/projects/:id/merge_request/:merge_request_id/changes

        Args:
            merge_request_id: 合并请求的 ID

        Returns:
            包含文件变更和差异信息的字典
        """
        return self._make_request(f'merge_request/{merge_request_id}/changes')

    def get_tapd_workitems(self, mr_iid: str, item_type: str = 'mr') -> List[Dict[str, Any]]:
        """
        获取与合并请求关联的 TAPD 工作项

        API: GET /api/v3/projects/:id/tapd_workitems

        Args:
            mr_iid: 合并请求的 IID
            item_type: 项目类型（默认：'mr'）

        Returns:
            TAPD 工作项字典列表
        """
        params = {
            'type': item_type,
            'iid': mr_iid
        }

        return self._make_request('tapd_workitems', params)

    def extract_mr_iids_from_commits(self, commits: List[Dict[str, Any]]) -> List[str]:
        """
        从提交标题中提取合并请求 IID

        Args:
            commits: 包含 'title' 字段的提交字典列表

        Returns:
            提取到的 MR IID 列表
        """
        # 正则表达式匹配 "merge request !" 后跟数字
        pattern = re.compile(r'merge request !(\d+)')
        title_list = [commit['title'] for commit in commits]

        # 从提交标题中提取 MR 编号
        extracted_numbers = [
            match.group(1)
            for match in map(pattern.search, title_list)
            if match
        ]

        return extracted_numbers

    def get_mr_iids_from_version_comparison(self, version: str, target: str = 'master') -> List[str]:
        """
        通过版本/分支对比获取合并请求 IID

        这是一个便捷方法，结合了分支对比和 MR IID 提取功能

        Args:
            version: 源版本/分支/标签
            target: 目标分支（默认：'master'）

        Returns:
            在对比中找到的合并请求 IID 列表
        """
        comparison_result = self.compare_branches(version, target)
        # print(f"对比结果：{comparison_result}")
        commits = comparison_result.get('commits', [])
        return self.extract_mr_iids_from_commits(commits)

    def get_recent_merged_mrs_by_iids(self, mr_iids: List[str], days_back: int = 30) -> List[Dict[str, Any]]:
        """
        根据 IID 获取最近合并的 MR，并过滤插件mr

        Args:
            mr_iids: 要过滤的 MR IID 列表
            days_back: 回溯天数（默认：30）

        Returns:
            包含附加字段的合并请求字典列表
        """
        # 计算日期过滤器
        # time_ago = datetime.now() - timedelta(days=days_back)
        # created_after_str = time_ago.strftime('%Y-%m-%dT%H:%M:%S+0000')

        # 获取 MR 列表
        mr_list = self.get_merge_requests(
            state='merged',
            target_branch='master',
            order_by='resolve_at',
            per_page=200,
            created_after=None,
            iids=mr_iids
        )

        # 过滤和格式化结果
        filtered_mrs = []
        for mr in mr_list:
            if str(mr['iid']) in mr_iids:
                is_plugin_mr = self.is_plugin_related_mr(mr['id'])
                if is_plugin_mr:
                    print(f"这是一个与插件相关的 MR，不进行收集！")
                else:
                    filtered_mrs.append({
                        'iid': mr['iid'],
                        'id': mr['id'],
                        'author': mr['author']['name'],
                        'title': mr.get('title', ''),
                        'description': mr.get('description', ''),
                        'created_at': mr.get('created_at', ''),
                        'merged_at': mr.get('merged_at', '')
                    })
        # print(f"过滤后的 MR 总数量：{len(filtered_mrs)}, 列表：{filtered_mrs}")
        print(f"过滤后的 MR 总数量：{len(filtered_mrs)}")

        return filtered_mrs

    def extract_changed_directories_from_mr(self, merge_request_id: int) -> List[str]:
        """
        从合并请求中提取变更目录列表

        Args:
            merge_request_id: 合并请求的 ID

        Returns:
            被修改的目录路径列表
        """
        changes = self.get_merge_request_changes(merge_request_id)
        files = changes.get('files', [])

        directories = []
        # pattern = re.compile(r"diff --git a/(.+?) b/\1")

        # for file_info in files:
        #     diff_content = file_info.get('diff', '')
        #     # 在差异中查找所有目录路径
        #     directories.extend(re.findall(pattern, diff_content))
        for file_info in files:
            path = file_info.get('new_path', '')
            if path:
                directories.append(path)

        # 去重并返回
        return list(set(directories))

    def is_plugin_related_mr(self, merge_request_id: int, plugin_prefix: str = "Plugin/") -> bool:
        """
        检查合并请求是否包含插件相关变更

        Args:
            merge_request_id: 合并请求的 ID
            plugin_prefix: 识别插件目录的前缀（默认："Plugin/"）

        Returns:
            如果 MR 包含插件变更返回 True，否则返回 False
        """
        directories = self.extract_changed_directories_from_mr(merge_request_id)
        return any(directory.startswith(plugin_prefix) for directory in directories)

    def build_mr_url(self, mr_iid: int) -> str:
        """
        根据 MR 的 IID 构建合并请求链接

        Args:
            mr_iid: 合并请求的 IID

        Returns:
            完整的 MR 链接 URL
        """
        return f"{self.base_url}/{self.project_path}/-/merge_requests/{mr_iid}"

    def build_mr_urls_from_iids(self, mr_iids: List[str]) -> Dict[str, str]:
        """
        批量构建 MR 链接

        Args:
            mr_iids: MR IID 列表

        Returns:
            IID 到 URL 的映射字典
        """
        return {iid: self.build_mr_url(int(iid)) for iid in mr_iids}

    def parse_mr_description(self, description: str) -> Dict[str, str]:
        """
        解析 MR 描述信息，提取关键字段

        支持的字段格式：
        【需求地址】- 必须，请在创建MR的工蜂上关联TAPD单即可
           * https://tapd.woa.com/tapd_fe/20422314/story/detail/1020422314124595139
        【实验报告地址】- 必须, 标注实验的具体地址即可
           * https://tab.woa.com/tab/experiment/exp-result?expGroupId=1540843...
        【是否预埋需求】- 必须，版本灰度期间是否生效
           * 是
        【集成测试关注点】- 必须, 集成测试阶段需要关注的功能点
           * 不涉及
        【开关说明】- 非必须， 明确开关系统、Key，取值，开关打开方式等
           * 无

        Args:
            description: MR 的描述内容

        Returns:
            包含解析字段的字典，格式如下：
            {
                'tapd_url': 'TAPD工作项链接',
                'experiment_report_url': '实验报告地址',
                'is_pre_embedded': '是否预埋需求（是/否）',
                'integration_test_focus': '集成测试关注点',
                'switch_description': '开关说明'
            }
        """
        result = {
            'tapd_url': '',
            'experiment_report_url': '',
            'is_pre_embedded': '',
            'integration_test_focus': '',
            'switch_description': ''
        }

        if not description:
            return result

        # 定义字段映射和对应的正则表达式
        field_patterns = {
            'tapd_url': {
                'pattern': r'【需求地址】[^*]*\*\s*(https://tapd\.woa\.com[^\s\n]*)',
                'fallback_pattern': r'(https://tapd\.woa\.com[^\s\n]*)'
            },
            'experiment_report_url': {
                'pattern': r'【实验报告地址】[^*]*\*\s*([^\n]*)',
                'fallback_pattern': r'(https://tab\.woa\.com[^\s\n]*)'
            },
            'is_pre_embedded': {
                'pattern': r'【是否预埋需求】[^*]*\*\s*([是否])',
                'fallback_pattern': None
            },
            'integration_test_focus': {
                'pattern': r'【集成测试关注点】[^*]*\*\s*([^\n]*)',
                'fallback_pattern': None
            },
            'switch_description': {
                'pattern': r'【开关说明】[^*]*\*\s*([^\n]*)',
                'fallback_pattern': None
            }
        }

        # 逐个提取字段
        for field_key, patterns in field_patterns.items():
            # 尝试主要模式
            match = re.search(patterns['pattern'], description, re.DOTALL)
            if match:
                result[field_key] = match.group(1).strip()
            elif patterns.get('fallback_pattern'):
                # 尝试备用模式
                fallback_match = re.search(patterns['fallback_pattern'], description)
                if fallback_match:
                    result[field_key] = fallback_match.group(1).strip()

        return result

    def get_branch_diff_mrs_with_details(self, from_branch: str, to_branch: str = 'master',
                                       days_back: int = 30) -> List[Dict[str, Any]]:
        """
        对比两个分支获取差异 MR，并解析每个 MR 的详细信息

        Args:
            from_branch: 源分支
            to_branch: 目标分支（默认：'master'）
            days_back: 回溯天数（默认：30）

        Returns:
            包含详细信息的 MR 列表，每个 MR 包含以下字段：
            [
                {
                    'iid': MR的IID,
                    'id': MR的ID,
                    'title': MR标题（需求）,
                    'creator': 需求创建人
                    'type': 需求类型
                    'author': MR作者（终端开发）,
                    'mr_url': MR链接,
                    'tapd_url': TAPD工作项链接（需求单）,
                    'tapd_workitems': TAPD工作项详细信息列表,
                    'is_pre_embedded': 是否预埋（是/否）,
                    'integration_test_focus': 测试关注重点,
                    'switch_description': 开关说明,
                    'experiment_report_url': 实验报告地址,
                    'created_at': 创建时间,
                    'merged_at': 合并时间
                }
            ]
        """
        # 1. 获取两个分支的差异 MR IID 列表
        mr_iids = self.get_mr_iids_from_version_comparison(from_branch, to_branch)

        if not mr_iids:
            print(f"未找到 {from_branch} 和 {to_branch} 之间的差异 MR")
            return []

        print(f"找到 {len(mr_iids)} 个差异 MR: {mr_iids}")

        # 2. 获取这些 MR 的基本信息（根据指定时间周期的mr列表转换mr_iid到mr_id），并过滤是否是插件mr
        recent_mrs = self.get_recent_merged_mrs_by_iids(mr_iids, days_back)
        print(f"获取到 {len(recent_mrs)} 个最近合并的 MR")

        # 3. 批量获取 TAPD 工作项信息
        print("正在获取 TAPD 工作项信息...")
        tapd_workitems_batch = self.get_tapd_workitems_batch([str(mr['iid']) for mr in recent_mrs])

        # 4. 为每个 MR 解析详细信息
        detailed_mrs = []
        for mr in recent_mrs:
            try:
                mr_iid_str = str(mr['iid'])

                # 解析 MR 描述
                parsed_desc = self.parse_mr_description(mr.get('description', ''))

                # 构建 MR 链接
                mr_url = self.build_mr_url(mr['iid'])

                # 获取 TAPD 工作项信息
                tapd_workitems = tapd_workitems_batch.get(mr_iid_str, [])

                tapd_url = ''
                if tapd_workitems:
                    tapd_urls, tapd_info = self.build_tapd_urls_from_workitems(tapd_workitems)
                    if tapd_urls:
                        # 拼接所有 TAPD 链接作为 需求单
                        tapd_url = "\n".join(tapd_urls.values())

                # 如果 API 没有获取到 TAPD 链接，尝试从描述中解析
                if not tapd_url:
                    tapd_url = parsed_desc.get('tapd_url', '')

                # 组装详细信息
                detailed_mr = {
                    'iid': mr['iid'],
                    'id': mr['id'],
                    'title': mr.get('title', ''),  # 需求
                    'creator': tapd_info.get('creator', ''),  # 需求创建人
                    'type': tapd_info.get('type', ''),  # 需求类型
                    'author': mr.get('author', ''),  # 终端开发
                    'mr_url': mr_url,  # MR链接
                    'tapd_url': tapd_url,  # 需求单
                    'tapd_workitems': tapd_workitems,  # TAPD 工作项详细信息
                    'is_pre_embedded': parsed_desc.get('is_pre_embedded', ''),  # 是否预埋
                    'integration_test_focus': parsed_desc.get('integration_test_focus', ''),  # 测试关注重点
                    'switch_description': parsed_desc.get('switch_description', ''),  # 开关说明
                    'experiment_report_url': parsed_desc.get('experiment_report_url', ''),  # 实验报告地址
                    'created_at': mr.get('created_at', ''),
                    'merged_at': mr.get('merged_at', '')
                }

                detailed_mrs.append(detailed_mr)
                tapd_status = "已关联" if tapd_url else "未关联"
                print(f"成功解析 MR !{mr['iid']}: {mr.get('title', '')} (TAPD: {tapd_status})")

            except Exception as e:
                print(f"解析 MR !{mr['iid']} 失败: {e}")
                continue

        return detailed_mrs

    def format_mr_analysis_result(self, mrs: List[Dict[str, Any]]) -> str:
        """
        格式化 MR 分析结果为易读的文本格式

        Args:
            mrs: get_branch_diff_mrs_with_details 返回的 MR 列表

        Returns:
            格式化的文本结果
        """
        if not mrs:
            return "未找到任何 MR 差异"

        # 统计信息
        total_mrs = len(mrs)
        tapd_linked_count = sum(1 for mr in mrs if mr['tapd_url'])
        pre_embedded_count = sum(1 for mr in mrs if mr['is_pre_embedded'] == '是')

        result_lines = [
            f"# MR 分析结果",
            f"",
            f"## 统计概览",
            f"- 总 MR 数量: {total_mrs}",
            f"- 已关联 TAPD: {tapd_linked_count} ({tapd_linked_count/total_mrs*100:.1f}%)",
            f"- 预埋需求: {pre_embedded_count} ({pre_embedded_count/total_mrs*100:.1f}%)",
            f"",
            f"## 详细列表",
            f""
        ]

        for i, mr in enumerate(mrs, 1):
            # 显示 TAPD 工作项信息
            tapd_info = "未关联"
            if mr['tapd_url']:
                tapd_info = mr['tapd_url']
                # 如果有多个 TAPD 工作项，显示数量
                if mr.get('tapd_workitems') and len(mr['tapd_workitems']) > 1:
                    tapd_info += f" (共{len(mr['tapd_workitems'])}个工作项)"

            result_lines.extend([
                f"### {i}. {mr['title']}",
                f"",
                f"- **需求**: {mr['title']}",
                f"- **需求单**: {tapd_info}",
                f"- **MR链接**: {mr['mr_url']}",
                f"- **终端开发**: {mr['author']}",
                f"- **是否预埋**: {mr['is_pre_embedded'] if mr['is_pre_embedded'] else '未填写'}",
                f"- **测试关注重点**: {mr['integration_test_focus'] if mr['integration_test_focus'] else '未填写'}",
                f"- **开关说明**: {mr['switch_description'] if mr['switch_description'] else '未填写'}",
                f"- **实验报告地址**: {mr['experiment_report_url'] if mr['experiment_report_url'] else '未填写'}",
                f"",
            ])

        return "\n".join(result_lines)

    def build_tapd_url(self, workspace_id: str, tapd_id: str) -> str:
        """
        根据 workspace_id 和 tapd_id 构建 TAPD 工作项链接

        Args:
            workspace_id: TAPD 工作空间 ID
            tapd_id: TAPD 工作项 ID

        Returns:
            完整的 TAPD 工作项链接 URL
        """
        return f"https://tapd.woa.com/tapd_fe/{workspace_id}/story/detail/{tapd_id}"

    def build_tapd_urls_from_workitems(self, workitems: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        从 TAPD 工作项列表批量构建 TAPD 链接

        Args:
            workitems: get_tapd_workitems 返回的工作项列表

        Returns:
            tapd_id 到 URL 的映射字典 和 tapd_info
        """
        urls = {}
        for item in workitems:
            tapd_id = str(item.get('tapd_id', ''))
            workspace_id = str(item.get('workspace_id', ''))
            tapd_info = self.tapd_client.get_simplified_ids_info(list([tapd_id]))
            if tapd_id and workspace_id:
                urls[tapd_id] = self.build_tapd_url(workspace_id, tapd_id)
        # 默认返回第一个tapd单的信息
        return urls, tapd_info[0]

    def get_tapd_workitems_batch(self, mr_iids: List[str], item_type: str = 'mr') -> Dict[str, List[Dict[str, Any]]]:
        """
        批量查询多个 MR 的 TAPD 工作项

        Args:
            mr_iids: 合并请求 IID 列表
            item_type: 项目类型（默认：'mr'）

        Returns:
            MR IID 到 TAPD 工作项列表的映射字典
        """
        result = {}
        for mr_iid in mr_iids:
            try:
                workitems = self.get_tapd_workitems(mr_iid, item_type)
                result[mr_iid] = workitems
            except GitClientError as e:
                print(f"获取 MR {mr_iid} 的 TAPD 工作项失败: {e}")
                result[mr_iid] = []
        return result

    @classmethod
    def create_default_client(cls, project_id: str = "126979",
                             private_token: str = "_zhCOZW_1nf3_bHORMGD",
                             project_path: str = "yyb-android/TencentMobileAssistant") -> 'GitClient':
        """
        使用默认配置创建 GitClient 实例

        Args:
            project_id: 项目 ID（默认 仓库 TencentMobileAssistant）
            private_token: 私钥 （默认lichenlin的私钥，个人私钥查看：https://git.woa.com/profile/account）
            project_path: 项目的全路径（默认：yyb-android/TencentMobileAssistant）

        Returns:
            配置好的 GitClient 实例
        """
        return cls(project_id=project_id, private_token=private_token, project_path=project_path)


# 向后兼容的便捷函数
def get_mr_iid_list_from_version(version: str,
                                project_id: str = "126979",
                                private_token: str = "_zhCOZW_1nf3_bHORMGD") -> List[str]:
    """
    向后兼容的遗留函数
    从版本对比中获取 MR IID 列表

    Args:
        version: 源版本/分支/标签
        project_id: Git 项目 ID
        private_token: 认证令牌

    Returns:
        合并请求 IID 列表
    """
    client = GitClient(project_id=project_id, private_token=private_token)
    return client.get_mr_iids_from_version_comparison(version)


if __name__ == "__main__":
    """
    GitClient 使用示例

    主要功能：对比两个分支获取差异 MR 并解析详细信息
    """
    git_client = GitClient.create_default_client()

    try:
        print("=== 分支差异 MR 分析 ===")
        detailed_mrs = git_client.get_branch_diff_mrs_with_details("release-8.9.9", "master", days_back=7)

        # if detailed_mrs:
        #     # 格式化输出结果
        #     formatted_result = git_client.format_mr_analysis_result(detailed_mrs)
        #     print(formatted_result)
        # else:
        #     print("未找到差异 MR")

    except GitClientError as e:
        print(f"分支差异 MR 分析失败: {e}")

    # mr_iids = git_client.get_mr_iids_from_version_comparison("release-8.9.9", "master")
    # print(mr_iids)

    # recent_mrs = git_client.get_recent_merged_mrs_by_iids(mr_iids, days_back=30)
    # print(recent_mrs)
