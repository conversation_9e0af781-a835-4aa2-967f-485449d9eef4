from common.client.wecom_client import WecomClient
from common.logs.logger import app_logger

WEBHOOK_KEY = 'a87f69e7-c781-4ce0-a556-308a985ce5c9'

class WecomBotTips:
    @staticmethod
    def webhook_send(chat_id: str, msg: str):
        wecom_client = WecomClient(WEBHOOK_KEY)
        try:
            response = wecom_client.send_message(chat_id, msg)
            app_logger.info(f'Message sent successfully: {response}')
            print(response)
        except Exception as e:
            app_logger.error(f'Failed to send message:{e}')
            print(f'Failed to send message:{e}')

    @staticmethod
    def send_evaluate_and_save_prompt(chat_id: str, ticket_id: str):
        tips_evaluate_and_save_prompt = f"""
【满意度回访】日志分析完成，请您对本次的分析进行评价！您的工单id为：{ticket_id}

=== 输入格式 ===

【满意度回访】
### 工单ID
{ticket_id}
### 满意度:
5星    4星    3星    2星    1星
（5星代表非常满意，1星代表非常不满意，星级越高代表越满意！）
### 是否需要查看分析过程
是/否

=== 输入示例 ===

【满意度回访】
### 工单ID
{ticket_id}
### 满意度
5星
### 是否需要查看分析过程
是
"""
        wecom_client = WecomClient(WEBHOOK_KEY)
        try:
            response = wecom_client.send_message(chat_id, tips_evaluate_and_save_prompt)
            app_logger.info(f'Message sent successfully: {response}')
        except Exception as e:
            app_logger.error(f'Failed to send message:{e}')

    @staticmethod
    def help_markdown():
        return """
# 【帮助】
```text
1. 日志分析 输入 1
2. 版本信息查询 输入 2
3. 灰度实验数据分析 输入 3
```
        """


    @staticmethod
    def log_tool_help_markdown():
        return """
# 【日志分析】
## 输入格式
```text
【日志分析】
### 日志链接（必填）
日志下载链接 
### 用户问题（必填）
用户的问题
### bug时间（可选）
yyyy-mm-dd hh:mm:ss 
### 是否需要改写prompt（可选）
是/否
```
## 输入示例
```text
【日志分析】
### 日志链接
https://cms.myapp.com/xy/yybtech/NGIT2mIu.zip
### 用户问题
发货失败
### bug时间
2025-05-13 14:10:00
### 是否需要改写prompt
是
```
"""

    @staticmethod
    def version_info_help_markdown():
        return """
# 【版本工具】
```text
意图识别 开发中
```
"""

    @staticmethod
    def gray_data_help_markdown():
        return """
# 【灰度实验数据分析】
## 输入格式
```text
【灰度实验数据分析】
### 版本号
填写 灰度实验的版本号。
版本灰度实验 填写：版本号。如 900
班车灰度实验 填写：班车+时间。如 班车0605
```
## 输入示例 --- 版本灰度
```text
【灰度实验数据分析】
### 版本号
900
```
## 输入示例 --- 班车灰度
```text
【灰度实验数据分析】
### 版本号
班车0605
```
"""

    @staticmethod
    def tips_log_analyze():
        return """
# 【日志分析】
## 输入格式
```text
【日志分析】
### 日志链接（必填）
日志下载链接 
### bug时间（可选）
yyyy-mm-dd hh:mm:ss 
### 场景（必填）
需要分析的场景
### 用户问题
用户的问题
### 是否需要改写prompt
是/否
```
## 输入示例
```text
【日志分析】
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### bug时间
2025-05-13 14:10:00
### 场景
下载
### 用户问题
下载失败
### 是否需要改写prompt
是
```
"""

    # 企微机器人超过5秒未响应，企业微信会重试
    @staticmethod
    def retry_text():
        return """
正在快马加鞭处理中...
"""

    @staticmethod
    def tips_evaluate_and_save_prompt(ticket_id: str):
        return f"""
#【满意度回访】日志分析完成，请您对本次的分析进行评价！您的工单id为：{ticket_id}
## 输入格式：
```text
### 工单ID
{ticket_id}
### 满意度:
5星    4星    3星    2星    1星
（5星代表非常满意，1星代表非常不满意，星级越高代表越满意！）
### 是否保存prompt
是/否
```
## 输入示例
```text
### 工单ID
{ticket_id}
### 满意度:
5星
### 是否保存提示
是
```
"""

    @staticmethod
    def tips_no_scene(scene: str):
        return f"""
# 【未找到日志分析场景「{scene}」】若需继续分析，请按以下 输入格式 输入，我们将为您进行分析。
## 输入格式
```text
【日志分析-未预设场景】
### 日志链接（必填）
日志下载链接
### 场景（必填）
定义场景名称。
### bug时间（可选）
yyyy-mm-dd hh:mm:ss
### 用户问题
用户的问题
### 描述日志分析知识的iwiki链接
描述如何分析日志的iwiki链接
```
## 输入示例
```text
【日志分析-未预设场景】
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### 场景
安装
### bug时间
2025-05-13 14:10:00
### 用户问题
安装失败
### 描述日志分析知识的iwiki链接
https://iwiki.woa.com/p/4014594307
```
"""


    @staticmethod
    def tips_rewrite_prompt(scene: str = ''):
        if scene:
            start_str = f'【重写预设场景「{scene}」Prompt】'
        else:
            start_str = '【重写Prompt】'
        return f"""
# {start_str}请按以下 输入格式 输入，我们将为您进行分析。
## 输入格式
```text
【重写Prompt】
### 日志链接（必填）
日志下载链接
### 场景（必填）
定义场景名称。
### bug时间（可选）
yyyy-mm-dd hh:mm:ss
### 用户问题
用户的问题
### 描述日志分析知识的iwiki链接
描述如何分析日志的iwiki链接
```
## 输入示例
```text
【重写Prompt】
### 日志链接
https://cms.myapp.com/xy/yybtech/74q9dxpn.zip
### 场景
安装
### bug时间
2025-05-13 14:10:00
### 用户问题
安装失败
### 描述日志分析知识的iwiki链接
https://iwiki.woa.com/p/4014594307
```
"""

    @staticmethod
    def tips_save_prompt():
        return """
# 【保存场景prompt】
## 输入格式
```text
【保存场景prompt】
### 场景（必填）
需要分析的场景
### 描述（可选）
下载失败第一版
### 过滤tag（必填）
 ["tag1", "tag2"]
### prompt（必填）
提示词
### 删除的日志行（可选）
[('tag1', 'content1'),('tag2', 'content2')]
```
## 输入示例
```text
【保存场景prompt】
### 场景
下载
### 描述
下载失败第一版
### 过滤tag
 ['tag1', 'tag2']
### prompt
提示词
### 删除的日志行
[('DownloadTag', 'fileType=PLUGIN'),('DownloadTag', 'generateTraceId')]
```
"""

    @staticmethod
    def delete_markdown():
        return """
我宣布，下班！！！
"""
